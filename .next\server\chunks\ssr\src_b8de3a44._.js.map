{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/env.ts"], "sourcesContent": ["// Environment variables validation and configuration\n\nimport { z } from 'zod'\n\n// Define the schema for environment variables\nconst envSchema = z.object({\n  // Node environment\n  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),\n  \n  // Supabase configuration\n  NEXT_PUBLIC_SUPABASE_URL: z.string().optional(),\n  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().optional(),\n  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),\n  \n  // Cloudinary configuration\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: z.string().optional(),\n  CLOUDINARY_API_KEY: z.string().optional(),\n  CLOUDINARY_API_SECRET: z.string().optional(),\n  \n  // Authentication (optional for build time)\n  NEXTAUTH_SECRET: z.string().optional(),\n  NEXTAUTH_URL: z.string().optional(),\n  \n  // Optional configuration\n  DEBUG: z.string().transform(val => val === 'true').default('false'),\n})\n\n// Parse and validate environment variables\nfunction validateEnv() {\n  try {\n    return envSchema.parse(process.env)\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`)\n      throw new Error(\n        `❌ Invalid environment variables:\\n${missingVars.join('\\n')}\\n\\n` +\n        `Please check your .env.local file and ensure all required variables are set.\\n` +\n        `See .env.example for reference.`\n      )\n    }\n    throw error\n  }\n}\n\n// Export validated environment variables\nexport const env = validateEnv()\n\n// Environment-specific configurations\nexport const config = {\n  isDevelopment: env.NODE_ENV === 'development',\n  isProduction: env.NODE_ENV === 'production',\n  isTest: env.NODE_ENV === 'test',\n  \n  // Database\n  database: {\n    url: env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co',\n    anonKey: env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key',\n    serviceRoleKey: env.SUPABASE_SERVICE_ROLE_KEY,\n  },\n\n  // File storage\n  cloudinary: {\n    cloudName: env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'placeholder',\n    apiKey: env.CLOUDINARY_API_KEY,\n    apiSecret: env.CLOUDINARY_API_SECRET,\n  },\n  \n  // Authentication\n  auth: {\n    secret: env.NEXTAUTH_SECRET,\n    url: env.NEXTAUTH_URL,\n  },\n  \n  // Debug mode\n  debug: env.DEBUG,\n} as const\n\n// Runtime environment checks\nexport function checkRequiredEnvVars() {\n  const requiredVars = [\n    'NEXT_PUBLIC_SUPABASE_URL',\n    'NEXT_PUBLIC_SUPABASE_ANON_KEY',\n    'NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME',\n  ]\n  \n  const missingVars = requiredVars.filter(varName => !process.env[varName])\n  \n  if (missingVars.length > 0) {\n    throw new Error(\n      `❌ Missing required environment variables: ${missingVars.join(', ')}\\n` +\n      `Please check your .env.local file.`\n    )\n  }\n}\n\n// Development-only environment checks\nexport function checkDevelopmentEnvVars() {\n  if (config.isDevelopment) {\n    const devVars = [\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n    ]\n    \n    const missingDevVars = devVars.filter(varName => !process.env[varName])\n    \n    if (missingDevVars.length > 0) {\n      console.warn(\n        `⚠️  Missing development environment variables: ${missingDevVars.join(', ')}\\n` +\n        `Some features may not work properly.`\n      )\n    }\n  }\n}\n\n// Production-only environment checks\nexport function checkProductionEnvVars() {\n  if (config.isProduction) {\n    const prodVars = [\n      'SUPABASE_SERVICE_ROLE_KEY',\n      'CLOUDINARY_API_KEY',\n      'CLOUDINARY_API_SECRET',\n      'NEXTAUTH_SECRET',\n      'NEXTAUTH_URL',\n    ]\n    \n    const missingProdVars = prodVars.filter(varName => !process.env[varName])\n    \n    if (missingProdVars.length > 0) {\n      throw new Error(\n        `❌ Missing production environment variables: ${missingProdVars.join(', ')}\\n` +\n        `These are required for production deployment.`\n      )\n    }\n  }\n}\n\n// Initialize environment validation\nexport function initializeEnv() {\n  try {\n    checkRequiredEnvVars()\n    checkDevelopmentEnvVars()\n    checkProductionEnvVars()\n    \n    if (config.debug) {\n      console.log('✅ Environment variables validated successfully')\n      console.log('📊 Configuration:', {\n        environment: env.NODE_ENV,\n        database: !!config.database.url,\n        cloudinary: !!config.cloudinary.cloudName,\n        auth: !!config.auth.secret,\n      })\n    }\n  } catch (error) {\n    console.error(error)\n    if (config.isProduction) {\n      process.exit(1)\n    }\n  }\n}\n\n// Export individual environment variables for convenience\nexport const {\n  NODE_ENV,\n  NEXT_PUBLIC_SUPABASE_URL,\n  NEXT_PUBLIC_SUPABASE_ANON_KEY,\n  SUPABASE_SERVICE_ROLE_KEY,\n  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,\n  CLOUDINARY_API_KEY,\n  CLOUDINARY_API_SECRET,\n  NEXTAUTH_SECRET,\n  NEXTAUTH_URL,\n  DEBUG,\n} = env\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;;;;;AAErD;;AAEA,8CAA8C;AAC9C,MAAM,YAAY,kKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzB,mBAAmB;IACnB,UAAU,kKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAe;QAAc;KAAO,EAAE,OAAO,CAAC;IAEhE,yBAAyB;IACzB,0BAA0B,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7C,+BAA+B,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClD,2BAA2B,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE9C,2BAA2B;IAC3B,mCAAmC,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACtD,oBAAoB,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvC,uBAAuB,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE1C,2CAA2C;IAC3C,iBAAiB,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,cAAc,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAEjC,yBAAyB;IACzB,OAAO,kKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA,MAAO,QAAQ,QAAQ,OAAO,CAAC;AAC7D;AAEA,2CAA2C;AAC3C,SAAS;IACP,IAAI;QACF,OAAO,UAAU,KAAK,CAAC,QAAQ,GAAG;IACpC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,kKAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,MAAM,cAAc,MAAM,MAAM,CAAC,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACnF,MAAM,IAAI,MACR,CAAC,kCAAkC,EAAE,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC,GACjE,CAAC,8EAA8E,CAAC,GAChF,CAAC,+BAA+B,CAAC;QAErC;QACA,MAAM;IACR;AACF;AAGO,MAAM,MAAM;AAGZ,MAAM,SAAS;IACpB,eAAe,IAAI,QAAQ,KAAK;IAChC,cAAc,IAAI,QAAQ,KAAK;IAC/B,QAAQ,IAAI,QAAQ,KAAK;IAEzB,WAAW;IACX,UAAU;QACR,KAAK,IAAI,wBAAwB,IAAI;QACrC,SAAS,IAAI,6BAA6B,IAAI;QAC9C,gBAAgB,IAAI,yBAAyB;IAC/C;IAEA,eAAe;IACf,YAAY;QACV,WAAW,IAAI,iCAAiC,IAAI;QACpD,QAAQ,IAAI,kBAAkB;QAC9B,WAAW,IAAI,qBAAqB;IACtC;IAEA,iBAAiB;IACjB,MAAM;QACJ,QAAQ,IAAI,eAAe;QAC3B,KAAK,IAAI,YAAY;IACvB;IAEA,aAAa;IACb,OAAO,IAAI,KAAK;AAClB;AAGO,SAAS;IACd,MAAM,eAAe;QACnB;QACA;QACA;KACD;IAED,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;IAExE,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,MAAM,IAAI,MACR,CAAC,0CAA0C,EAAE,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC,GACvE,CAAC,kCAAkC,CAAC;IAExC;AACF;AAGO,SAAS;IACd,IAAI,OAAO,aAAa,EAAE;QACxB,MAAM,UAAU;YACd;YACA;YACA;SACD;QAED,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;QAEtE,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,QAAQ,IAAI,CACV,CAAC,+CAA+C,EAAE,eAAe,IAAI,CAAC,MAAM,EAAE,CAAC,GAC/E,CAAC,oCAAoC,CAAC;QAE1C;IACF;AACF;AAGO,SAAS;IACd,IAAI,OAAO,YAAY,EAAE;QACvB,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA;SACD;QAED,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,UAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;QAExE,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,IAAI,MACR,CAAC,4CAA4C,EAAE,gBAAgB,IAAI,CAAC,MAAM,EAAE,CAAC,GAC7E,CAAC,6CAA6C,CAAC;QAEnD;IACF;AACF;AAGO,SAAS;IACd,IAAI;QACF;QACA;QACA;QAEA,IAAI,OAAO,KAAK,EAAE;YAChB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,qBAAqB;gBAC/B,aAAa,IAAI,QAAQ;gBACzB,UAAU,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;gBAC/B,YAAY,CAAC,CAAC,OAAO,UAAU,CAAC,SAAS;gBACzC,MAAM,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM;YAC5B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC;QACd,IAAI,OAAO,YAAY,EAAE;YACvB,QAAQ,IAAI,CAAC;QACf;IACF;AACF;AAGO,MAAM,EACX,QAAQ,EACR,wBAAwB,EACxB,6BAA6B,EAC7B,yBAAyB,EACzB,iCAAiC,EACjC,kBAAkB,EAClB,qBAAqB,EACrB,eAAe,EACf,YAAY,EACZ,KAAK,EACN,GAAG", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { config } from './env'\n\n// Create Supabase client with validated environment variables\nexport const supabase = createClient(\n  config.database.url,\n  config.database.anonKey,\n  {\n    auth: {\n      autoRefreshToken: true,\n      persistSession: true,\n      detectSessionInUrl: true\n    },\n    db: {\n      schema: 'public'\n    },\n    global: {\n      headers: {\n        'X-Client-Info': 'revantad-store@1.0.0'\n      }\n    }\n  }\n)\n\n// Database Types\nexport interface Product {\n  id: string\n  name: string\n  image_url?: string\n  net_weight: string\n  price: number\n  stock_quantity: number\n  category: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CustomerDebt {\n  id: string\n  customer_name: string\n  customer_family_name: string\n  product_name: string\n  product_price: number\n  quantity: number\n  total_amount: number\n  debt_date: string\n  created_at: string\n  updated_at: string\n}\n\n// Product Categories\nexport const PRODUCT_CATEGORIES = [\n  'Snacks',\n  'Canned Goods',\n  'Beverages',\n  'Personal Care',\n  'Household Items',\n  'Condiments',\n  'Rice & Grains',\n  'Instant Foods',\n  'Dairy Products',\n  'Others'\n] as const\n\nexport type ProductCategory = typeof PRODUCT_CATEGORIES[number]\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EACjC,iHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,GAAG,EACnB,iHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,OAAO,EACvB;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;IACtB;IACA,IAAI;QACF,QAAQ;IACV;IACA,QAAQ;QACN,SAAS;YACP,iBAAiB;QACnB;IACF;AACF;AA8BK,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/contexts/SettingsContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\n\n// Settings Types\nexport interface StoreSettings {\n  name: string\n  address: string\n  phone: string\n  email: string\n  website: string\n  currency: string\n  timezone: string\n  businessHours: {\n    open: string\n    close: string\n  }\n  operatingDays: string[]\n  businessRegistration: {\n    registrationNumber: string\n    taxId: string\n    businessType: string\n    registrationDate: string\n  }\n  locations: Array<{\n    id: number\n    name: string\n    address: string\n    phone: string\n    isMain: boolean\n  }>\n  branding: {\n    logo: string | null\n    primaryColor: string\n    secondaryColor: string\n    slogan: string\n  }\n}\n\nexport interface ProfileSettings {\n  firstName: string\n  lastName: string\n  email: string\n  phone: string\n  role: string\n  avatar: string | null\n  bio: string\n  dateOfBirth: string\n  address: string\n  emergencyContact: {\n    name: string\n    phone: string\n    relationship: string\n  }\n  preferences: {\n    language: string\n    timezone: string\n    dateFormat: string\n    numberFormat: string\n  }\n}\n\nexport interface NotificationSettings {\n  lowStock: boolean\n  newDebt: boolean\n  paymentReceived: boolean\n  dailyReport: boolean\n  weeklyReport: boolean\n  emailNotifications: boolean\n  smsNotifications: boolean\n  pushNotifications: boolean\n  channels: {\n    email: string\n    sms: string\n    webhook: string\n  }\n  customRules: Array<{\n    id: number\n    name: string\n    condition: string\n    action: string\n    enabled: boolean\n  }>\n  templates: {\n    lowStock: string\n    newDebt: string\n    paymentReceived: string\n  }\n}\n\nexport interface SecuritySettings {\n  twoFactorAuth: boolean\n  sessionTimeout: string\n  passwordExpiry: string\n  loginAttempts: string\n  currentPassword: string\n  newPassword: string\n  confirmPassword: string\n  apiKeys: Array<{\n    id: number\n    name: string\n    key: string\n    created: string\n    lastUsed: string\n    permissions: string[]\n  }>\n  loginHistory: Array<{\n    id: number\n    timestamp: string\n    ip: string\n    device: string\n    location: string\n    success: boolean\n  }>\n  passwordPolicy: {\n    minLength: number\n    requireUppercase: boolean\n    requireLowercase: boolean\n    requireNumbers: boolean\n    requireSymbols: boolean\n  }\n}\n\nexport interface AppearanceSettings {\n  theme: string\n  language: string\n  dateFormat: string\n  numberFormat: string\n  colorScheme: {\n    primary: string\n    secondary: string\n    accent: string\n    background: string\n    surface: string\n  }\n  layout: {\n    sidebarPosition: string\n    density: string\n    showAnimations: boolean\n    compactMode: boolean\n  }\n  typography: {\n    fontFamily: string\n    fontSize: string\n    fontWeight: string\n  }\n}\n\nexport interface BackupSettings {\n  autoBackup: boolean\n  backupFrequency: string\n  retentionDays: string\n  lastBackup: string\n  cloudStorage: {\n    provider: string\n    bucket: string\n    accessKey: string\n    secretKey: string\n  }\n  backupHistory: Array<{\n    id: number\n    timestamp: string\n    size: string\n    status: string\n    type: string\n  }>\n  verification: {\n    enabled: boolean\n    lastVerified: string\n    status: string\n  }\n}\n\nexport interface AllSettings {\n  store: StoreSettings\n  profile: ProfileSettings\n  notifications: NotificationSettings\n  security: SecuritySettings\n  appearance: AppearanceSettings\n  backup: BackupSettings\n}\n\ninterface SettingsContextType {\n  settings: AllSettings\n  updateSettings: (section: keyof AllSettings, newSettings: Partial<AllSettings[keyof AllSettings]>) => void\n  saveSettings: () => Promise<void>\n  resetSettings: (section?: keyof AllSettings) => void\n  isLoading: boolean\n  hasUnsavedChanges: boolean\n}\n\nconst SettingsContext = createContext<SettingsContextType | undefined>(undefined)\n\n// Default settings\nconst defaultSettings: AllSettings = {\n  store: {\n    name: 'Revantad Store',\n    address: '123 Barangay Street, Manila, Philippines',\n    phone: '+63 ************',\n    email: '<EMAIL>',\n    website: 'https://revantadstore.com',\n    currency: 'PHP',\n    timezone: 'Asia/Manila',\n    businessHours: { open: '06:00', close: '22:00' },\n    operatingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],\n    businessRegistration: {\n      registrationNumber: 'REG-2024-001',\n      taxId: 'TAX-*********',\n      businessType: 'Retail',\n      registrationDate: '2024-01-01',\n    },\n    locations: [{\n      id: 1,\n      name: 'Main Store',\n      address: '123 Barangay Street, Manila, Philippines',\n      phone: '+63 ************',\n      isMain: true,\n    }],\n    branding: {\n      logo: null,\n      primaryColor: '#22c55e',\n      secondaryColor: '#facc15',\n      slogan: 'Your Neighborhood Store',\n    },\n  },\n  profile: {\n    firstName: 'Admin',\n    lastName: 'User',\n    email: '<EMAIL>',\n    phone: '+63 ************',\n    role: 'Store Owner',\n    avatar: null,\n    bio: 'Experienced store owner managing Revantad Store operations.',\n    dateOfBirth: '1990-01-01',\n    address: '123 Barangay Street, Manila, Philippines',\n    emergencyContact: {\n      name: 'Emergency Contact',\n      phone: '+63 ************',\n      relationship: 'Family',\n    },\n    preferences: {\n      language: 'en',\n      timezone: 'Asia/Manila',\n      dateFormat: 'MM/DD/YYYY',\n      numberFormat: 'en-US',\n    },\n  },\n  notifications: {\n    lowStock: true,\n    newDebt: true,\n    paymentReceived: true,\n    dailyReport: false,\n    weeklyReport: true,\n    emailNotifications: true,\n    smsNotifications: false,\n    pushNotifications: true,\n    channels: {\n      email: '<EMAIL>',\n      sms: '+63 ************',\n      webhook: '',\n    },\n    customRules: [{\n      id: 1,\n      name: 'Critical Stock Alert',\n      condition: 'stock < 5',\n      action: 'email + sms',\n      enabled: true,\n    }],\n    templates: {\n      lowStock: 'Product {{productName}} is running low ({{currentStock}} remaining)',\n      newDebt: 'New debt recorded for {{customerName}}: ₱{{amount}}',\n      paymentReceived: 'Payment received from {{customerName}}: ₱{{amount}}',\n    },\n  },\n  security: {\n    twoFactorAuth: false,\n    sessionTimeout: '30',\n    passwordExpiry: '90',\n    loginAttempts: '5',\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n    apiKeys: [{\n      id: 1,\n      name: 'Main API Key',\n      key: 'sk_live_***************',\n      created: '2024-01-01',\n      lastUsed: '2024-01-20',\n      permissions: ['read', 'write'],\n    }],\n    loginHistory: [{\n      id: 1,\n      timestamp: '2024-01-20T10:30:00Z',\n      ip: '***********',\n      device: 'Chrome on Windows',\n      location: 'Manila, Philippines',\n      success: true,\n    }],\n    passwordPolicy: {\n      minLength: 8,\n      requireUppercase: true,\n      requireLowercase: true,\n      requireNumbers: true,\n      requireSymbols: true,\n    },\n  },\n  appearance: {\n    theme: 'light',\n    language: 'en',\n    dateFormat: 'MM/DD/YYYY',\n    numberFormat: 'en-US',\n    colorScheme: {\n      primary: '#22c55e',\n      secondary: '#facc15',\n      accent: '#3b82f6',\n      background: '#ffffff',\n      surface: '#f8fafc',\n    },\n    layout: {\n      sidebarPosition: 'left',\n      density: 'comfortable',\n      showAnimations: true,\n      compactMode: false,\n    },\n    typography: {\n      fontFamily: 'Inter',\n      fontSize: 'medium',\n      fontWeight: 'normal',\n    },\n  },\n  backup: {\n    autoBackup: true,\n    backupFrequency: 'daily',\n    retentionDays: '30',\n    lastBackup: '2024-01-20T10:30:00Z',\n    cloudStorage: {\n      provider: 'local',\n      bucket: '',\n      accessKey: '',\n      secretKey: '',\n    },\n    backupHistory: [\n      {\n        id: 1,\n        timestamp: '2024-01-20T10:30:00Z',\n        size: '2.5 MB',\n        status: 'completed',\n        type: 'automatic',\n      },\n      {\n        id: 2,\n        timestamp: '2024-01-19T10:30:00Z',\n        size: '2.4 MB',\n        status: 'completed',\n        type: 'automatic',\n      }\n    ],\n    verification: {\n      enabled: true,\n      lastVerified: '2024-01-20T10:35:00Z',\n      status: 'verified',\n    },\n  },\n}\n\nexport function SettingsProvider({ children }: { children: ReactNode }) {\n  const [settings, setSettings] = useState<AllSettings>(defaultSettings)\n  const [isLoading, setIsLoading] = useState(false)\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)\n\n  // Load settings from localStorage on mount\n  useEffect(() => {\n    const savedSettings = localStorage.getItem('revantad-settings')\n    if (savedSettings) {\n      try {\n        const parsed = JSON.parse(savedSettings)\n        setSettings({ ...defaultSettings, ...parsed })\n      } catch (error) {\n        console.error('Error loading settings:', error)\n      }\n    }\n  }, [])\n\n  const updateSettings = (section: keyof AllSettings, newSettings: Partial<AllSettings[keyof AllSettings]>) => {\n    setSettings(prev => ({\n      ...prev,\n      [section]: { ...prev[section], ...newSettings }\n    }))\n    setHasUnsavedChanges(true)\n  }\n\n  const saveSettings = async () => {\n    setIsLoading(true)\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1000))\n      \n      // Save to localStorage\n      localStorage.setItem('revantad-settings', JSON.stringify(settings))\n      \n      setHasUnsavedChanges(false)\n      \n      // Apply theme changes immediately\n      if (settings.appearance.theme === 'dark') {\n        document.documentElement.classList.add('dark')\n      } else {\n        document.documentElement.classList.remove('dark')\n      }\n      \n      console.log('Settings saved successfully:', settings)\n    } catch (error) {\n      console.error('Error saving settings:', error)\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const resetSettings = (section?: keyof AllSettings) => {\n    if (section) {\n      setSettings(prev => ({\n        ...prev,\n        [section]: defaultSettings[section]\n      }))\n    } else {\n      setSettings(defaultSettings)\n    }\n    setHasUnsavedChanges(true)\n  }\n\n  return (\n    <SettingsContext.Provider value={{\n      settings,\n      updateSettings,\n      saveSettings,\n      resetSettings,\n      isLoading,\n      hasUnsavedChanges\n    }}>\n      {children}\n    </SettingsContext.Provider>\n  )\n}\n\nexport function useSettings() {\n  const context = useContext(SettingsContext)\n  if (context === undefined) {\n    throw new Error('useSettings must be used within a SettingsProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AA+LA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEvE,mBAAmB;AACnB,MAAM,kBAA+B;IACnC,OAAO;QACL,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,eAAe;YAAE,MAAM;YAAS,OAAO;QAAQ;QAC/C,eAAe;YAAC;YAAU;YAAW;YAAa;YAAY;YAAU;SAAW;QACnF,sBAAsB;YACpB,oBAAoB;YACpB,OAAO;YACP,cAAc;YACd,kBAAkB;QACpB;QACA,WAAW;YAAC;gBACV,IAAI;gBACJ,MAAM;gBACN,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;SAAE;QACF,UAAU;YACR,MAAM;YACN,cAAc;YACd,gBAAgB;YAChB,QAAQ;QACV;IACF;IACA,SAAS;QACP,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,MAAM;QACN,QAAQ;QACR,KAAK;QACL,aAAa;QACb,SAAS;QACT,kBAAkB;YAChB,MAAM;YACN,OAAO;YACP,cAAc;QAChB;QACA,aAAa;YACX,UAAU;YACV,UAAU;YACV,YAAY;YACZ,cAAc;QAChB;IACF;IACA,eAAe;QACb,UAAU;QACV,SAAS;QACT,iBAAiB;QACjB,aAAa;QACb,cAAc;QACd,oBAAoB;QACpB,kBAAkB;QAClB,mBAAmB;QACnB,UAAU;YACR,OAAO;YACP,KAAK;YACL,SAAS;QACX;QACA,aAAa;YAAC;gBACZ,IAAI;gBACJ,MAAM;gBACN,WAAW;gBACX,QAAQ;gBACR,SAAS;YACX;SAAE;QACF,WAAW;YACT,UAAU;YACV,SAAS;YACT,iBAAiB;QACnB;IACF;IACA,UAAU;QACR,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,aAAa;QACb,iBAAiB;QACjB,SAAS;YAAC;gBACR,IAAI;gBACJ,MAAM;gBACN,KAAK;gBACL,SAAS;gBACT,UAAU;gBACV,aAAa;oBAAC;oBAAQ;iBAAQ;YAChC;SAAE;QACF,cAAc;YAAC;gBACb,IAAI;gBACJ,WAAW;gBACX,IAAI;gBACJ,QAAQ;gBACR,UAAU;gBACV,SAAS;YACX;SAAE;QACF,gBAAgB;YACd,WAAW;YACX,kBAAkB;YAClB,kBAAkB;YAClB,gBAAgB;YAChB,gBAAgB;QAClB;IACF;IACA,YAAY;QACV,OAAO;QACP,UAAU;QACV,YAAY;QACZ,cAAc;QACd,aAAa;YACX,SAAS;YACT,WAAW;YACX,QAAQ;YACR,YAAY;YACZ,SAAS;QACX;QACA,QAAQ;YACN,iBAAiB;YACjB,SAAS;YACT,gBAAgB;YAChB,aAAa;QACf;QACA,YAAY;YACV,YAAY;YACZ,UAAU;YACV,YAAY;QACd;IACF;IACA,QAAQ;QACN,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,YAAY;QACZ,cAAc;YACZ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,WAAW;QACb;QACA,eAAe;YACb;gBACE,IAAI;gBACJ,WAAW;gBACX,MAAM;gBACN,QAAQ;gBACR,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,WAAW;gBACX,MAAM;gBACN,QAAQ;gBACR,MAAM;YACR;SACD;QACD,cAAc;YACZ,SAAS;YACT,cAAc;YACd,QAAQ;QACV;IACF;AACF;AAEO,SAAS,iBAAiB,EAAE,QAAQ,EAA2B;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,YAAY;oBAAE,GAAG,eAAe;oBAAE,GAAG,MAAM;gBAAC;YAC9C,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC,SAA4B;QAClD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,QAAQ,EAAE;oBAAE,GAAG,IAAI,CAAC,QAAQ;oBAAE,GAAG,WAAW;gBAAC;YAChD,CAAC;QACD,qBAAqB;IACvB;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,uBAAuB;YACvB,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC;YAEzD,qBAAqB;YAErB,kCAAkC;YAClC,IAAI,SAAS,UAAU,CAAC,KAAK,KAAK,QAAQ;gBACxC,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC,OAAO;gBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5C;YAEA,QAAQ,GAAG,CAAC,gCAAgC;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS;YACX,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,CAAC,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBACrC,CAAC;QACH,OAAO;YACL,YAAY;QACd;QACA,qBAAqB;IACvB;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAC/B;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useTheme } from 'next-themes'\nimport { useState, useEffect } from 'react'\n\nimport {\n  AdminHeader,\n  Sidebar,\n  ProductsSection,\n  DebtsSection,\n  DashboardStats,\n  FamilyGallery,\n  APIGraphing,\n  History,\n  Calendar,\n  Settings,\n  ProtectedRoute\n} from '@/components'\nimport type { DashboardStats as DashboardStatsType } from '@/types'\n\nexport default function AdminPage() {\n  const [activeSection, setActiveSection] = useState('dashboard')\n  const { resolvedTheme } = useTheme()\n  const [stats, setStats] = useState<DashboardStatsType>({\n    totalProducts: 0,\n    totalDebts: 0,\n    totalDebtAmount: 0,\n    lowStockItems: 0,\n    recentProducts: [],\n    recentDebts: []\n  })\n\n  useEffect(() => {\n    fetchStats()\n  }, [])\n\n  const fetchStats = async () => {\n    try {\n      // Fetch products\n      const productsRes = await fetch('/api/products')\n      const productsData = await productsRes.json()\n      const products = productsData.products || []\n\n      // Fetch debts\n      const debtsRes = await fetch('/api/debts')\n      const debtsData = await debtsRes.json()\n      const debts = debtsData.debts || []\n\n      // Calculate stats\n      const totalDebtAmount = debts.reduce((sum: number, debt: { total_amount: number }) => sum + debt.total_amount, 0)\n      const lowStockProducts = products.filter((product: { stock_quantity: number }) => product.stock_quantity < 10).length\n\n      setStats({\n        totalProducts: products.length,\n        totalDebts: debts.length,\n        totalDebtAmount,\n        lowStockItems: lowStockProducts,\n        recentProducts: products.slice(0, 5),\n        recentDebts: debts.slice(0, 5)\n      })\n    } catch (error) {\n      console.error('Error fetching stats:', error)\n    }\n  }\n\n  const renderContent = () => {\n    switch (activeSection) {\n      case 'products':\n        return <ProductsSection onStatsUpdate={fetchStats} />\n      case 'debts':\n        return <DebtsSection onStatsUpdate={fetchStats} />\n      case 'family-gallery':\n        return <FamilyGallery />\n      case 'api-graphing':\n        return <APIGraphing stats={stats} />\n      case 'history':\n        return <History />\n      case 'calendar':\n        return <Calendar />\n      case 'settings':\n        return <Settings />\n      default:\n        return <DashboardStats stats={stats} />\n    }\n  }\n\n  const getPageTitle = () => {\n    switch (activeSection) {\n      case 'dashboard':\n        return 'Dashboard'\n      case 'products':\n        return 'Product Lists'\n      case 'debts':\n        return 'Customer Debt Management'\n      case 'family-gallery':\n        return 'Family Gallery'\n      case 'api-graphing':\n        return 'API Graphing & Visuals'\n      case 'history':\n        return 'History'\n      case 'calendar':\n        return 'Calendar'\n      case 'settings':\n        return 'Settings'\n      default:\n        return 'Dashboard'\n    }\n  }\n\n  const getPageDescription = () => {\n    switch (activeSection) {\n      case 'dashboard':\n        return 'Overview of your Revantad Store'\n      case 'products':\n        return 'Manage your product lists with CRUD operations'\n      case 'debts':\n        return 'Track customer debt and payments'\n      case 'family-gallery':\n        return 'Manage family photos and memories'\n      case 'api-graphing':\n        return 'Visual analytics and business insights'\n      case 'history':\n        return 'View transaction and activity history'\n      case 'calendar':\n        return 'Manage events and schedules'\n      case 'settings':\n        return 'Configure your store settings'\n      default:\n        return 'Overview of your Revantad Store'\n    }\n  }\n\n  return (\n    <ProtectedRoute>\n      <div\n        className=\"min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300\"\n        style={{\n          backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#f9fafb'\n        }}\n      >\n        {/* Facebook-style Header */}\n        <AdminHeader\n          activeSection={activeSection}\n          setActiveSection={setActiveSection}\n        />\n\n        <div className=\"flex pt-16\">\n          {/* Updated Sidebar */}\n          <Sidebar activeSection={activeSection} setActiveSection={setActiveSection} />\n\n          {/* Main Content */}\n          <main\n            className=\"flex-1 transition-colors duration-300 main-content-scroll\"\n            style={{\n              backgroundColor: resolvedTheme === 'dark' ? '#0f172a' : '#ffffff',\n              height: 'calc(100vh - 4rem)',\n              overflowY: 'auto',\n              overflowX: 'hidden'\n            }}\n          >\n            <div className=\"p-8\">\n              <div className=\"mb-8\">\n                <h1\n                  className=\"text-3xl font-bold transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#f8fafc' : '#1f2937'\n                  }}\n                >\n                  {getPageTitle()}\n                </h1>\n                <p\n                  className=\"mt-2 transition-colors duration-300\"\n                  style={{\n                    color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'\n                  }}\n                >\n                  {getPageDescription()}\n                </p>\n              </div>\n              {renderContent()}\n            </div>\n          </main>\n        </div>\n      </div>\n    </ProtectedRoute>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QACrD,eAAe;QACf,YAAY;QACZ,iBAAiB;QACjB,eAAe;QACf,gBAAgB,EAAE;QAClB,aAAa,EAAE;IACjB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,iBAAiB;YACjB,MAAM,cAAc,MAAM,MAAM;YAChC,MAAM,eAAe,MAAM,YAAY,IAAI;YAC3C,MAAM,WAAW,aAAa,QAAQ,IAAI,EAAE;YAE5C,cAAc;YACd,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,QAAQ,UAAU,KAAK,IAAI,EAAE;YAEnC,kBAAkB;YAClB,MAAM,kBAAkB,MAAM,MAAM,CAAC,CAAC,KAAa,OAAmC,MAAM,KAAK,YAAY,EAAE;YAC/G,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC,UAAwC,QAAQ,cAAc,GAAG,IAAI,MAAM;YAErH,SAAS;gBACP,eAAe,SAAS,MAAM;gBAC9B,YAAY,MAAM,MAAM;gBACxB;gBACA,eAAe;gBACf,gBAAgB,SAAS,KAAK,CAAC,GAAG;gBAClC,aAAa,MAAM,KAAK,CAAC,GAAG;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,mLAAA,CAAA,kBAAe;oBAAC,eAAe;;;;;;YACzC,KAAK;gBACH,qBAAO,8OAAC,6KAAA,CAAA,eAAY;oBAAC,eAAe;;;;;;YACtC,KAAK;gBACH,qBAAO,8OAAC,+KAAA,CAAA,gBAAa;;;;;YACvB,KAAK;gBACH,qBAAO,8OAAC,2KAAA,CAAA,cAAW;oBAAC,OAAO;;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,mKAAA,CAAA,UAAO;;;;;YACjB,KAAK;gBACH,qBAAO,8OAAC,qKAAA,CAAA,WAAQ;;;;;YAClB,KAAK;gBACH,qBAAO,8OAAC,qKAAA,CAAA,WAAQ;;;;;YAClB;gBACE,qBAAO,8OAAC,iLAAA,CAAA,iBAAc;oBAAC,OAAO;;;;;;QAClC;IACF;IAEA,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,iLAAA,CAAA,iBAAc;kBACb,cAAA,8OAAC;YACC,WAAU;YACV,OAAO;gBACL,iBAAiB,kBAAkB,SAAS,YAAY;YAC1D;;8BAGA,8OAAC,2KAAA,CAAA,cAAW;oBACV,eAAe;oBACf,kBAAkB;;;;;;8BAGpB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,mKAAA,CAAA,UAAO;4BAAC,eAAe;4BAAe,kBAAkB;;;;;;sCAGzD,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAiB,kBAAkB,SAAS,YAAY;gCACxD,QAAQ;gCACR,WAAW;gCACX,WAAW;4BACb;sCAEA,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DAEC;;;;;;0DAEH,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO,kBAAkB,SAAS,YAAY;gDAChD;0DAEC;;;;;;;;;;;;oCAGJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}